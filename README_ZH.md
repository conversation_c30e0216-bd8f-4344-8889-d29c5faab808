<h1 align="center">Jellyfin Plugin MetaTube</h1>
<p align="center"><b><a href="README.md">English</a> | 简体中文</b></p>

<p align="center">
<img alt="Plugin Banner" src="https://metatube-community.github.io/images/banner-dark.png"/>
<br/>
<br/>

<a href="https://github.com/metatube-community/jellyfin-plugin-metatube/actions">
<img alt="GitHub Workflow Status" src="https://img.shields.io/github/actions/workflow/status/metatube-community/jellyfin-plugin-metatube/dotnetcore.yml?branch=main&logo=github">
</a>
<a href="https://github.com/metatube-community/jellyfin-plugin-metatube/search?l=c%23">
<img alt="GitHub top language" src="https://img.shields.io/github/languages/top/metatube-community/jellyfin-plugin-metatube?color=%23239120&label=.NET&logo=csharp">
</a>
<a href="https://github.com/metatube-community/jellyfin-plugin-metatube/blob/main/LICENSE">
<img alt="License" src="https://img.shields.io/github/license/metatube-community/jellyfin-plugin-metatube">
</a>
<a href="https://github.com/metatube-community/jellyfin-plugin-metatube">
<img alt="gitHub Stars" src="https://img.shields.io/github/stars/metatube-community/jellyfin-plugin-metatube?style=flat">
</a>
<a href="https://github.com/metatube-community/jellyfin-plugin-metatube">
<img alt="Downloads" src="https://img.shields.io/github/downloads/metatube-community/jellyfin-plugin-metatube/total">
</a>
<a href="https://github.com/metatube-community/jellyfin-plugin-metatube/releases">
<img alt="Releases" src="https://img.shields.io/github/v/release/metatube-community/jellyfin-plugin-metatube?include_prereleases&logo=smartthings">
</a>
</p>

## 关于

为 Jellyfin/Emby 开发的超级好用的元数据插件。

## 特性

- 完整数据：包括标题、**简介**、演员、标签、**评分**等内容。
- 完整搜索：支持通过众多的刮削源搜索影片和演员信息。
- 预告功能：无需下载完整预告视频即可**在线观看预告片**。
- 计划任务：自动整理影片标签以及在后台自动更新插件。
- 人脸识别：内置的人脸识别以人脸为中心裁剪海报图像。
- 自动翻译：支持将特定的元数据内容翻译成需要的语言。

## 平台

[![Jellyfin](https://img.shields.io/static/v1?color=%2300A4DC&style=for-the-badge&label=Jellyfin&logo=jellyfin&message=10.9.x)](https://jellyfin.org/)
[![Emby](https://img.shields.io/static/v1?color=%2352B54B&style=for-the-badge&label=Emby&logo=emby&message=4.8.x)](https://emby.media/)

_注意：本项目仅支持 Jellyfin/Emby 稳定版。_

## 文档

- [插件安装](https://metatube-community.github.io/wiki/plugin-installation/)
- [后端部署](https://metatube-community.github.io/wiki/server-deployment/)
- [命名规范](https://metatube-community.github.io/wiki/naming-rules/)
- [自动翻译](https://metatube-community.github.io/wiki/auto-translation/)
- [源码编译](https://metatube-community.github.io/wiki/build-from-source/)
- [数据来源](https://metatube-community.github.io/wiki/metadata-providers/)

完整的文档以及使用方法，请参阅 [Wiki](https://metatube-community.github.io/wiki/)。

## 社区

有任何问题欢迎来 [Discussions](https://github.com/metatube-community/jellyfin-plugin-metatube/discussions) 提问讨论。

## 许可

本插件项目在 [MIT](https://github.com/metatube-community/jellyfin-plugin-metatube/blob/main/LICENSE) 许可授权下发行。此外，如果使用本项目表明还额外接受以下条款：

- 本插件仅供学习以及技术交流使用
- 请勿在公共社交平台上宣传此项目
- 使用本软件时请遵守当地法律法规
- 法律及使用后果由使用者自己承担
- 禁止将本软件用于任何的商业用途

## 星星

[![Star History Chart](https://api.star-history.com/svg?repos=metatube-community/jellyfin-plugin-metatube&type=Date)](https://star-history.com/#metatube-community/jellyfin-plugin-metatube&Date)
