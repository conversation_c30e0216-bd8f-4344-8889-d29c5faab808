name: .NET

on:
  workflow_dispatch:

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup .NET Core
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.x'

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Generate Version
        id: shell
        run: |
          echo "version=$(date -u '+%Y.%-m%d.%-H%M.0')" >> $GITHUB_OUTPUT

      - name: Build Plugins
        run: |
          dotnet build --configuration Release -p:Version=${{ steps.shell.outputs.version }}
          dotnet build --configuration Release.Emby -p:Version=${{ steps.shell.outputs.version }}

      - name: Generate Manifest
        run: |
          python3 scripts/manifest.py Jellyfin.Plugin.MetaTube/bin/Jellyfin.MetaTube@v${{ steps.shell.outputs.version }}.zip

      - name: Publish Manifest
        run: |
          git config --global user.name  'metatube-bot'
          git config --global user.email '<EMAIL>'
          git remote set-url origin https://x-access-token:${{ secrets.GITHUB_TOKEN }}@github.com/${GITHUB_REPOSITORY}

          git checkout --orphan dist
          git rm -rf .
          git add manifest.json
          git commit -m "Auto Updated by Actions"
          git push -f -u origin dist

      - name: Upload Plugins
        uses: softprops/action-gh-release@v2
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          body: _Auto Released by Actions_
          draft: false
          tag_name: v${{ steps.shell.outputs.version }}
          files: |
            Jellyfin.Plugin.MetaTube/bin/Jellyfin.MetaTube@v${{ steps.shell.outputs.version }}.zip
            Jellyfin.Plugin.MetaTube/bin/Emby.MetaTube@v${{ steps.shell.outputs.version }}.zip
