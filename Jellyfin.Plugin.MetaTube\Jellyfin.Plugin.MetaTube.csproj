<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <ImplicitUsings>enable</ImplicitUsings>
        <Configurations>Debug;Release;Debug.Emby;Release.Emby</Configurations>
        <Platforms>AnyCPU</Platforms>
        <AssemblyName>MetaTube</AssemblyName>
        <Authors>MetaTube</Authors>
        <Description>MetaTube Plugin for Jellyfin/Emby</Description>
        <Version>$([System.DateTime]::UtcNow.ToString(yyyy.Mdd.Hmm.0))</Version>
        <Copyright>Copyright © 2023 MetaTube</Copyright>
        <RepositoryType>Git</RepositoryType>
        <RepositoryUrl>https://github.com/metatube-community/jellyfin-plugin-metatube.git</RepositoryUrl>
        <PackageProjectUrl>https://github.com/metatube-community/jellyfin-plugin-metatube</PackageProjectUrl>
        <PackageLicenseUrl>https://github.com/metatube-community/jellyfin-plugin-metatube/blob/main/LICENSE</PackageLicenseUrl>
        <PackageIcon>thumb.png</PackageIcon>
        <PackageId>MetaTube</PackageId>
        <Company>MetaTube</Company>
        <Product>MetaTube</Product>
    </PropertyGroup>

    <PropertyGroup>
        <TargetFramework Condition="'$(Configuration)'=='Debug' or '$(Configuration)'=='Release'">net8.0</TargetFramework>
        <TargetFramework Condition="'$(Configuration)'=='Debug.Emby' or '$(Configuration)'=='Release.Emby'">net6.0</TargetFramework>
    </PropertyGroup>

    <PropertyGroup Condition="'$(Configuration)'=='Debug.Emby' or '$(Configuration)'=='Release.Emby'">
        <DefineConstants>__EMBY__</DefineConstants>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="System.Memory" Version="4.5.5"/>
    </ItemGroup>

    <ItemGroup Condition="'$(Configuration)'=='Debug' or '$(Configuration)'=='Release'">
        <PackageReference Include="Jellyfin.Controller" Version="10.9.0"/>
        <PackageReference Include="Jellyfin.Model" Version="10.9.0"/>
    </ItemGroup>

    <ItemGroup Condition="'$(Configuration)'=='Debug.Emby' or '$(Configuration)'=='Release.Emby'">
        <PackageReference Include="MediaBrowser.Server.Core" Version="********"/>
    </ItemGroup>

    <ItemGroup Condition="'$(Configuration)'=='Debug' or '$(Configuration)'=='Release'">
        <None Remove="Configuration\configPage.html"/>
        <EmbeddedResource Include="Configuration\configPage.html"/>
    </ItemGroup>

    <ItemGroup Condition="'$(Configuration)'=='Debug.Emby' or '$(Configuration)'=='Release.Emby'">
        <None Remove="thumb.png"/>
        <EmbeddedResource Include="thumb.png"/>
    </ItemGroup>

    <Target Name="Zip" AfterTargets="PostBuildEvent" Condition="'$(Configuration)'=='Release' or '$(Configuration)'=='Release.Emby'">
        <ItemGroup>
            <FilesToDelete Include="$(BaseOutputPath)Jellyfin.MetaTube*.zip" Condition="'$(Configuration)'=='Release'"/>
            <FilesToDelete Include="$(BaseOutputPath)Emby.MetaTube*.zip" Condition="'$(Configuration)'=='Release.Emby'"/>
            <TempZipDirectory Include="$(OutputPath)output"/>
        </ItemGroup>
        <Delete Files="@(FilesToDelete)"/>
        <Copy SourceFiles="$(OutputPath)$(AssemblyName).dll" DestinationFolder="@(TempZipDirectory)"/>
        <ZipDirectory SourceDirectory="@(TempZipDirectory)" DestinationFile="$(BaseOutputPath)Jellyfin.MetaTube@v$(Version).zip" Condition="'$(Configuration)'=='Release'"/>
        <ZipDirectory SourceDirectory="@(TempZipDirectory)" DestinationFile="$(BaseOutputPath)Emby.MetaTube@v$(Version).zip" Condition="'$(Configuration)'=='Release.Emby'"/>
        <RemoveDir Directories="@(TempZipDirectory)"/>
    </Target>

</Project>
