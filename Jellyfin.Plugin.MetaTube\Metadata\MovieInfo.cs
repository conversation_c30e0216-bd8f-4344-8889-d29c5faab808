using System.Text.Json.Serialization;

namespace Jellyfin.Plugin.MetaTube.Metadata;

public class MovieInfo : MovieSearchResult
{
    [JsonPropertyName("big_cover_url")]
    public string BigCoverUrl { get; set; }

    [Json<PERSON>ropertyName("big_thumb_url")]
    public string BigThumbUrl { get; set; }

    [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("director")]
    public string Director { get; set; }

    [JsonPropertyName("genres")]
    public string[] Genres { get; set; }

    [JsonPropertyName("maker")]
    public string Maker { get; set; }

    [JsonPropertyName("preview_images")]
    public string[] PreviewImages { get; set; }

    [Json<PERSON>ropertyName("preview_video_hls_url")]
    public string PreviewVideoHlsUrl { get; set; }

    [JsonPropertyName("preview_video_url")]
    public string PreviewVideoUrl { get; set; }

    [JsonPropertyName("label")]
    public string Label { get; set; }

    [JsonPropertyName("runtime")]
    public int Runtime { get; set; }

    [Json<PERSON>ropertyName("series")]
    public string Series { get; set; }

    [<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>("summary")]
    public string Summary { get; set; }
}